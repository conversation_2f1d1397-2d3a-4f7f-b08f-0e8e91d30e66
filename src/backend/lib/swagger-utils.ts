/**
 * Utility functions for Swagger/OpenAPI documentation
 */

/**
 * Tags that should be excluded from public API documentation
 */
export const INTERNAL_TAGS = [
  'Admin Authentication',
  'Admin Management', 
  'Webhook', // Internal webhook processing
  'Billing',
  'User Dashboard',
  'User Logs',
  'Templates',
  'Webhooks', // <PERSON><PERSON> webhooks
  'API Keys'
];

/**
 * Path prefixes that should be excluded from public API documentation
 */
export const INTERNAL_PATH_PREFIXES = [
  '/admin',
  '/api/webhook/email', // Internal email processing
  '/api/billing',
  '/api/dashboard',
  '/api/templates',
  '/api/logs',
  '/api/keys',
  '/webhooks/mollie' // <PERSON><PERSON> webhook endpoints
];

/**
 * Tags that should be included in public API documentation
 */
export const PUBLIC_TAGS = [
  'User Domains',
  'User Aliases', 
  'User Webhooks'
];

/**
 * Path prefixes that should be included in public API documentation
 */
export const PUBLIC_PATH_PREFIXES = [
  '/api/domains',
  '/api/aliases',
  '/api/webhooks'
];

/**
 * Check if a tag should be included in public documentation
 */
export function isPublicTag(tagName: string): boolean {
  return PUBLIC_TAGS.includes(tagName);
}

/**
 * Check if a path should be included in public documentation
 */
export function isPublicPath(path: string): boolean {
  return PUBLIC_PATH_PREFIXES.some(prefix => path.startsWith(prefix));
}

/**
 * Check if a tag should be excluded from public documentation
 */
export function isInternalTag(tagName: string): boolean {
  return INTERNAL_TAGS.includes(tagName);
}

/**
 * Check if a path should be excluded from public documentation
 */
export function isInternalPath(path: string): boolean {
  return INTERNAL_PATH_PREFIXES.some(prefix => path.startsWith(prefix));
}

/**
 * Filter OpenAPI specification to only include public routes
 */
export function filterPublicRoutes(swaggerObject: any): any {
  if (!swaggerObject || typeof swaggerObject !== 'object') {
    return swaggerObject;
  }

  const filteredPaths: any = {};

  // Filter paths to only include public ones
  Object.keys(swaggerObject.paths || {}).forEach(path => {
    if (isPublicPath(path)) {
      const pathItem = swaggerObject.paths[path];
      if (pathItem && typeof pathItem === 'object') {
        // Ensure each operation has valid schemas
        const filteredPathItem: any = {};
        Object.keys(pathItem).forEach(method => {
          const operation = pathItem[method];
          if (operation && typeof operation === 'object') {
            filteredPathItem[method] = operation;
          }
        });
        filteredPaths[path] = filteredPathItem;
      }
    }
  });

  // Filter tags to only include public ones
  const filteredTags = (swaggerObject.tags || []).filter((tag: any) =>
    tag && tag.name && isPublicTag(tag.name)
  );

  return {
    ...swaggerObject,
    paths: filteredPaths,
    tags: filteredTags,
    info: {
      ...swaggerObject.info,
      'x-logo': {
        url: '/src/frontend/assets/images/logo-light.png',
        altText: 'EmailConnect.eu'
      }
    }
  };
}

/**
 * Create a schema object that excludes documentation when not in public API mode
 */
export function createConditionalSchema(schema: any, isPublic: boolean = false): any {
  if (!isPublic) {
    // Remove tags and other OpenAPI-specific properties for internal routes
    const { tags, summary, description, ...restSchema } = schema;
    return restSchema;
  }
  return schema;
}

/**
 * Environment check for documentation inclusion
 */
export function shouldIncludeInDocs(): boolean {
  // Only include full documentation in development or when explicitly enabled
  return process.env.NODE_ENV === 'development' || process.env.INCLUDE_INTERNAL_DOCS === 'true';
}
