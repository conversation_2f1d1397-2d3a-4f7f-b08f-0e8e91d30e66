import { OpenAPIV3 } from 'openapi-types';

// Admin route paths - these will be generated by the auth route handlers
export const adminPaths: Record<string, OpenAPIV3.PathItemObject> = {
  '/admin/login': {
    get: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Page',
      description: 'Serves the HTML page for admin login. If already logged in (cookie present), may redirect to an admin dashboard.',
      responses: {
        '200': {
          description: 'Admin login HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '302': {
          description: 'Redirects to admin dashboard if already authenticated.',
        }
      },
    },
    post: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Attempt',
      description: 'Handles admin login credentials. On success, sets an HTTP-only cookie `admin_token` and returns a success message with the token. On failure, returns an error.',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/AdminLoginPayload' },
          },
        },
      },
      responses: {
        '200': {
          description: 'Admin login successful. Cookie `admin_token` is set.',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/AdminLoginSuccessResponse' },
            },
          },
        },
        '400': { description: 'Missing username or password.', content: { 'application/json': { schema: { $ref: '#/components/schemas/ErrorResponse' }}}},
        '401': { description: 'Invalid credentials.', content: { 'application/json': { schema: { $ref: '#/components/schemas/ErrorResponse' }}}},
        '500': { description: 'Internal server error during authentication.', content: { 'application/json': { schema: { $ref: '#/components/schemas/ErrorResponse' }}}},
      },
    },
  },
  '/admin/logout': {
    post: {
      tags: ['Admin Authentication'],
      summary: 'Admin Logout',
      description: 'Clears the admin authentication cookie and redirects to the login page.',
      responses: {
        '302': {
          description: 'Successfully logged out, redirects to /admin/login. Clears `admin_token` cookie.',
          headers: {
            Location: {
              schema: { type: 'string' },
              description: 'URL to redirect to (typically /admin/login)'
            }
          }
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' }
      }
    }
  },
  '/admin/dashboard': {
    get: {
      tags: ['Admin Management'],
      summary: 'Admin Dashboard',
      description: 'Protected admin dashboard page.',
      security: [{ adminAuth: [] }],
      responses: {
        '200': {
          description: 'Admin dashboard HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' },
        '302': {
          description: 'Redirects to login if not authenticated.',
        }
      },
    }
  },
  '/admin/domains': {
    get: {
      tags: ['Admin Management'],
      summary: 'View all domains (Admin)',
      description: 'Retrieves a list of all domains across all users. Requires admin privileges (admin_token cookie).',
      security: [{ adminAuth: [] }],
      parameters: [
        { name: 'page', in: 'query', schema: { type: 'integer', default: 1 } },
        { name: 'limit', in: 'query', schema: { type: 'integer', default: 20 } },
      ],
      responses: {
        '200': {
          description: 'A list of all domains.',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: { $ref: '#/components/schemas/AdminDomainView' },
              },
            },
          },
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' },
        '403': { $ref: '#/components/responses/ForbiddenAdmin' },
      },
    },
  },
  '/admin/logs/webhook': {
    get: {
      tags: ['Admin Management'],
      summary: 'View webhook logs (Admin)',
      description: 'Retrieves logs of all webhook activities. Requires admin privileges (admin_token cookie).',
      security: [{ adminAuth: [] }],
      parameters: [
        { name: 'domainId', in: 'query', schema: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' }, description: 'Filter by domain ID' },
        { name: 'status', in: 'query', schema: { type: 'string', enum: ['received', 'processed', 'failed'] }, description: 'Filter by status' },
        { name: 'startDate', in: 'query', schema: { type: 'string', format: 'date-time' }, description: 'Start date for logs' },
        { name: 'endDate', in: 'query', schema: { type: 'string', format: 'date-time' }, description: 'End date for logs' },
        { name: 'page', in: 'query', schema: { type: 'integer', default: 1 } },
        { name: 'limit', in: 'query', schema: { type: 'integer', default: 50 } },
      ],
      responses: {
        '200': {
          description: 'A list of webhook logs.',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: { $ref: '#/components/schemas/WebhookLog' },
              },
            },
          },
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' },
        '403': { $ref: '#/components/responses/ForbiddenAdmin' },
      },
    },
  },
  '/admin/logs/failed': {
    get: {
      tags: ['Admin Management'],
      summary: 'View failed delivery logs (Admin)',
      description: 'Retrieves logs of all failed email deliveries or processing attempts. Requires admin privileges (admin_token cookie).',
      security: [{ adminAuth: [] }],
      parameters: [
        { name: 'domainId', in: 'query', schema: { type: 'string', example: 'cmbw6hopl002uru1qqyx7b18a' }, description: 'Filter by domain ID' },
        { name: 'errorCode', in: 'query', schema: { type: 'string' }, description: 'Filter by a specific error code or message part' },
        { name: 'startDate', in: 'query', schema: { type: 'string', format: 'date-time' }, description: 'Start date for logs' },
        { name: 'endDate', in: 'query', schema: { type: 'string', format: 'date-time' }, description: 'End date for logs' },
        { name: 'page', in: 'query', schema: { type: 'integer', default: 1 } },
        { name: 'limit', in: 'query', schema: { type: 'integer', default: 50 } },
      ],
      responses: {
        '200': {
          description: 'A list of failed logs.',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: { $ref: '#/components/schemas/WebhookLog' },
              },
            },
          },
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' },
        '403': { $ref: '#/components/responses/ForbiddenAdmin' },
      },
    },
  },
  '/admin/stats/delivery': {
    get: {
      tags: ['Admin Management'],
      summary: 'Get delivery statistics (Admin)',
      description: 'Retrieves aggregated statistics about email delivery. Requires admin privileges (admin_token cookie).',
      security: [{ adminAuth: [] }],
      parameters: [
        { name: 'period', in: 'query', schema: { type: 'string', enum: ['daily', 'weekly', 'monthly'] }, description: 'Aggregation period' },
        { name: 'startDate', in: 'query', schema: { type: 'string', format: 'date' }, description: 'Start date for statistics' },
        { name: 'endDate', in: 'query', schema: { type: 'string', format: 'date' }, description: 'End date for statistics' },
      ],
      responses: {
        '200': {
          description: 'Delivery statistics.',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: { $ref: 'DeliveryStatistic#' },
              },
            },
          },
        },
        '401': { $ref: '#/components/responses/UnauthorizedAdmin' },
        '403': { $ref: '#/components/responses/ForbiddenAdmin' },
      },
    },
  },
};

// Webhook paths
export const webhookPaths: Record<string, OpenAPIV3.PathItemObject> = {
  '/api/webhook/email': {
    post: {
      tags: ['Webhook'],
      summary: 'Receive incoming email',
      description: 'Endpoint for Postfix (or other MTA) to forward emails to. This typically requires IP whitelisting or a secret token in the URL/header, not standard user API keys.',
      requestBody: {
        description: 'Raw email data or structured format from MTA',
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                recipient: { type: 'string', format: 'email' },
                sender: { type: 'string', format: 'email' },
                raw_email: { type: 'string', description: 'Full raw email content' },
              },
              required: ['recipient', 'sender', 'raw_email'],
            }
          },
          'message/rfc822': {
            schema: {
              type: 'string',
              format: 'binary'
            }
          }
        }
      },
      responses: {
        '202': { description: 'Email accepted for processing.' },
        '400': { description: 'Invalid email data', content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}},
        '401': { description: 'Unauthorized (e.g., invalid secret)', content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}},
        '500': { description: 'Internal server error processing email', content: { 'application/json': { schema: { $ref: 'ErrorResponse#' }}}},
      }
    }
  }
};
