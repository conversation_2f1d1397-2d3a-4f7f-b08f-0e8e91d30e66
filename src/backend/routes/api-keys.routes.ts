import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { userOrApiKeyAuthMiddleware } from '../lib/auth.js'
import { ApiKeyService } from '../services/auth/api-key.service.js'
import { ScopeValidatorService } from '../services/auth/scope-validator.service.js'

export async function apiKeyRoutes(fastify: FastifyInstance) {
  const apiKeyService = new ApiKeyService()

  // Generate new API key
  fastify.post('/api-keys', {
    preHandler: userOrApiKeyAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'Generate new API key',
      description: 'Generate a new API key for the authenticated user.',
      body: {
      type: 'object',
      required: ['name', 'scopes'],
      properties: {
      name: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      description: 'User-friendly name for the API key'
      },
        scopes: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1,
            description: 'Array of scopes for the API key'
          },
          description: {
            type: 'string',
            maxLength: 500,
            description: 'Optional description for the API key'
          }
        }
      },
      response: {
        '201': {
          description: 'API key generated successfully.',
          type: 'object',
          properties: {
            message: { type: 'string' },
            apiKey: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                keyPrefix: { type: 'string' },
                key: { type: 'string', description: 'Full API key - only shown once' },
                scopes: { type: 'array', items: { type: 'string' } },
                description: { type: 'string', nullable: true },
                createdAt: { type: 'string', format: 'date-time' },
                lastUsedAt: { type: 'string', format: 'date-time', nullable: true }
              }
            }
          }
        },
        '400': { $ref: '#/components/schemas/ErrorResponse' },
        '401': { $ref: '#/components/schemas/ErrorResponse' },
        '500': { $ref: '#/components/schemas/ErrorResponse' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { name, scopes, description } = request.body as { name: string; scopes: string[]; description?: string }
    const user = (request as any).user

    const result = await apiKeyService.generateApiKey({
      name,
      scopes,
      description,
      userId: user.id
    })

    if (!result.success) {
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: result.error || 'Failed to generate API key'
      })
    }

    return reply.status(201).send({
      message: 'API key generated successfully',
      apiKey: result.apiKey
    })
  })

  // List user's API keys
  fastify.get('/api-keys', {
    preHandler: userOrApiKeyAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'List API keys',
      description: 'List all API keys for the authenticated user.',
      response: {
        '200': {
          description: 'API keys retrieved successfully.',
          type: 'object',
          properties: {
            apiKeys: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  keyPrefix: { type: 'string' },
                  scopes: { type: 'array', items: { type: 'string' } },
                  description: { type: 'string', nullable: true },
                  createdAt: { type: 'string', format: 'date-time' },
                  lastUsedAt: { type: 'string', format: 'date-time', nullable: true }
                }
              }
            }
          }
        },
        '401': { $ref: '#/components/schemas/ErrorResponse' },
        '500': { $ref: '#/components/schemas/ErrorResponse' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user

    const result = await apiKeyService.listApiKeys(user.id)

    if (!result.success) {
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: result.error || 'Failed to retrieve API keys'
      })
    }

    return reply.send({
      apiKeys: result.apiKeys || []
    })
  })

  // Revoke API key
  fastify.delete('/api-keys/:id', {
    preHandler: userOrApiKeyAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'Revoke API key',
      description: 'Revoke (delete) an API key.',
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: 'API key ID' }
        }
      },
      response: {
        '200': {
          description: 'API key revoked successfully.',
          type: 'object',
          properties: {
            message: { type: 'string' }
          }
        },
        '404': { $ref: '#/components/schemas/ErrorResponse' },
        '401': { $ref: '#/components/schemas/ErrorResponse' },
        '500': { $ref: '#/components/schemas/ErrorResponse' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { id } = request.params as { id: string }
    const user = (request as any).user

    const result = await apiKeyService.revokeApiKey(id, user.id)

    if (!result.success) {
      const statusCode = result.error === 'API key not found' ? 404 : 500
      return reply.status(statusCode).send({
        statusCode,
        error: statusCode === 404 ? 'Not Found' : 'Internal Server Error',
        message: result.error || 'Failed to revoke API key'
      })
    }

    return reply.send({
      message: 'API key revoked successfully'
    })
  })

  // Get available scopes
  fastify.get('/api-keys/scopes', {
    preHandler: userOrApiKeyAuthMiddleware,
    schema: {
      tags: ['API Keys'],
      summary: 'Get available scopes',
      description: 'Get list of available scopes and preset configurations.',
      response: {
        '200': {
          description: 'Available scopes retrieved successfully.',
          type: 'object',
          properties: {
            scopes: {
              type: 'object',
              additionalProperties: { type: 'string' },
              description: 'Available scopes with descriptions'
            },
            presets: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  scopes: { type: 'array', items: { type: 'string' } },
                  description: { type: 'string' }
                }
              },
              description: 'Preset scope configurations'
            }
          }
        },
        '401': { $ref: '#/components/schemas/ErrorResponse' }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const availableScopes = ScopeValidatorService.getAvailableScopes()
    
    const presets = {
      'full-access': {
        scopes: ScopeValidatorService.PRESET_SCOPES['full-access'],
        description: 'Full access to all API endpoints'
      },
      'read-only': {
        scopes: ScopeValidatorService.PRESET_SCOPES['read-only'],
        description: 'Read-only access to domains, aliases, and webhooks'
      },
      'api-user': {
        scopes: ScopeValidatorService.PRESET_SCOPES['api-user'],
        description: 'Limited access for API users (domains: read/config, aliases/webhooks: full access)'
      }
    }

    return reply.send({
      scopes: availableScopes,
      presets
    })
  })

  fastify.log.info('API key routes registered')
}
